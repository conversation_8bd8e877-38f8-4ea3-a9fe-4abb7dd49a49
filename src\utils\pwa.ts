// PWA 相关工具函数

export interface PWAInstallPrompt extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

// 注册 Service Worker
export async function registerServiceWorker(): Promise<ServiceWorkerRegistration | null> {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/',
        updateViaCache: 'none'
      });

      console.log('Service Worker 注册成功:', registration);

      // 检查更新
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        if (newWorker) {
          console.log('发现新的 Service Worker');
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              console.log('新的 Service Worker 已安装，等待激活');
              // 可以在这里触发更新提示
              window.dispatchEvent(new CustomEvent('sw-update-available'));
            }
          });
        }
      });

      // 监听 Service Worker 控制器变化
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        console.log('Service Worker 控制器已更改，重新加载页面');
        window.location.reload();
      });

      return registration;
    } catch (error) {
      console.error('Service Worker 注册失败:', error);
      return null;
    }
  } else {
    console.log('当前浏览器不支持 Service Worker');
    return null;
  }
}

// 检查是否为 PWA 环境
export function isPWA(): boolean {
  return window.matchMedia('(display-mode: standalone)').matches ||
         (window.navigator as any).standalone ||
         document.referrer.includes('android-app://');
}

// 检查是否为移动设备
export function isMobile(): boolean {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

// 检查是否为 iOS 设备
export function isIOS(): boolean {
  return /iPad|iPhone|iPod/.test(navigator.userAgent);
}

// 检查是否支持 PWA 安装
export function supportsPWAInstall(): boolean {
  return 'serviceWorker' in navigator && 'PushManager' in window;
}

// 获取安装状态
export function getInstallStatus(): 'not-supported' | 'installed' | 'installable' | 'not-installable' {
  if (!supportsPWAInstall()) {
    return 'not-supported';
  }

  if (isPWA()) {
    return 'installed';
  }

  // 检查是否有待处理的安装提示
  const hasInstallPrompt = localStorage.getItem('pwa-install-prompt-available') === 'true';
  if (hasInstallPrompt) {
    return 'installable';
  }

  return 'not-installable';
}

// 显示安装提示
export async function showInstallPrompt(prompt: PWAInstallPrompt): Promise<boolean> {
  try {
    await prompt.prompt();
    const { outcome } = await prompt.userChoice;
    
    if (outcome === 'accepted') {
      console.log('用户接受了安装提示');
      localStorage.setItem('pwa-installed', 'true');
      return true;
    } else {
      console.log('用户拒绝了安装提示');
      localStorage.setItem('pwa-install-dismissed', Date.now().toString());
      return false;
    }
  } catch (error) {
    console.error('显示安装提示时出错:', error);
    return false;
  }
}

// 检查是否应该显示安装提示
export function shouldShowInstallPrompt(): boolean {
  // 如果已经安装，不显示
  if (isPWA() || localStorage.getItem('pwa-installed') === 'true') {
    return false;
  }

  // 检查用户是否最近拒绝过安装
  const lastDismissed = localStorage.getItem('pwa-install-dismissed');
  if (lastDismissed) {
    const dismissedTime = parseInt(lastDismissed);
    const daysSinceDismissed = (Date.now() - dismissedTime) / (1000 * 60 * 60 * 24);
    
    // 如果在7天内拒绝过，不显示
    if (daysSinceDismissed < 7) {
      return false;
    }
  }

  return true;
}

// 更新 Service Worker
export function updateServiceWorker(): void {
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.ready.then((registration) => {
      registration.update();
    });
  }
}

// 发送消息给 Service Worker
export function sendMessageToSW(message: any): Promise<any> {
  return new Promise((resolve, reject) => {
    if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      const messageChannel = new MessageChannel();
      
      messageChannel.port1.onmessage = (event) => {
        resolve(event.data);
      };

      navigator.serviceWorker.controller.postMessage(message, [messageChannel.port2]);
    } else {
      reject(new Error('Service Worker 不可用'));
    }
  });
}

// 获取 Service Worker 版本
export async function getServiceWorkerVersion(): Promise<string | null> {
  try {
    const version = await sendMessageToSW({ type: 'GET_VERSION' });
    return version?.version || null;
  } catch (error) {
    console.error('获取 Service Worker 版本失败:', error);
    return null;
  }
}

// 清理缓存
export async function clearCache(): Promise<void> {
  if ('caches' in window) {
    try {
      const cacheNames = await caches.keys();
      await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      );
      console.log('缓存已清理');
    } catch (error) {
      console.error('清理缓存失败:', error);
    }
  }
}

// 检查网络状态
export function getNetworkStatus(): 'online' | 'offline' {
  return navigator.onLine ? 'online' : 'offline';
}

// 监听网络状态变化
export function onNetworkStatusChange(callback: (status: 'online' | 'offline') => void): () => void {
  const handleOnline = () => callback('online');
  const handleOffline = () => callback('offline');

  window.addEventListener('online', handleOnline);
  window.addEventListener('offline', handleOffline);

  return () => {
    window.removeEventListener('online', handleOnline);
    window.removeEventListener('offline', handleOffline);
  };
}

// PWA 分析数据
export interface PWAAnalytics {
  isInstalled: boolean;
  installDate?: string;
  launchCount: number;
  lastLaunch?: string;
  version?: string;
}

// 获取 PWA 分析数据
export function getPWAAnalytics(): PWAAnalytics {
  const isInstalled = isPWA();
  const installDate = localStorage.getItem('pwa-install-date');
  const launchCount = parseInt(localStorage.getItem('pwa-launch-count') || '0');
  const lastLaunch = localStorage.getItem('pwa-last-launch');
  const version = localStorage.getItem('pwa-version');

  return {
    isInstalled,
    installDate: installDate || undefined,
    launchCount,
    lastLaunch: lastLaunch || undefined,
    version: version || undefined
  };
}

// 记录 PWA 启动
export function recordPWALaunch(): void {
  const currentCount = parseInt(localStorage.getItem('pwa-launch-count') || '0');
  localStorage.setItem('pwa-launch-count', (currentCount + 1).toString());
  localStorage.setItem('pwa-last-launch', new Date().toISOString());

  if (isPWA() && !localStorage.getItem('pwa-install-date')) {
    localStorage.setItem('pwa-install-date', new Date().toISOString());
  }
}
