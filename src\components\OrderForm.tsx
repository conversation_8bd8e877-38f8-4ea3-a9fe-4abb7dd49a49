'use client';

import { useState, useEffect } from 'react';
import { useOrderStore } from '@/store/orderStore';
import { CustomerInfo, AddressInfo, OrderInfo, OrderItem, Order } from '@/types/order';
import {
  generateOrderId,
  generateOrderNumber,
  generateItemId,
  calculateOrderSubtotal,
  calculateOrderTotal,
  validateEmail,
  validatePhone
} from '@/utils/orderUtils';
import { Plus, Trash2, User, MapPin, FileText } from 'lucide-react';
import { ProductSelector } from './ProductSelector';
import { DatePicker } from './DatePicker';
import { CalculatedProduct } from '@/types/product';

export function OrderForm() {
  const { currentOrder, selectedTemplate, setCurrentOrder } = useOrderStore();

  const [customer, setCustomer] = useState<CustomerInfo>({
    name: '',
    phone: '',
    email: ''
  });

  const [address, setAddress] = useState<AddressInfo>({
    shippingAddress: '',
    billingAddress: ''
  });

  const [orderInfo, setOrderInfo] = useState<OrderInfo>({
    id: '',
    orderNumber: '',
    date: new Date().toISOString().split('T')[0],
    items: [],
    subtotal: 0,
    tax: 0,
    discount: 0,
    total: 0,
    notes: '',
    paymentMethod: 'cash',
    paymentStatus: 'pending'
  });

  // 初始化订单信息
  useEffect(() => {
    if (!orderInfo.id) {
      setOrderInfo(prev => ({
        ...prev,
        id: generateOrderId(),
        orderNumber: generateOrderNumber()
      }));
    }
  }, []);

  // 重置表单到初始状态
  const resetForm = () => {
    setCustomer({
      name: '',
      phone: '',
      email: ''
    });

    setAddress({
      shippingAddress: '',
      billingAddress: ''
    });

    setOrderInfo({
      id: generateOrderId(),
      orderNumber: generateOrderNumber(),
      date: new Date().toISOString().split('T')[0],
      items: [],
      subtotal: 0,
      tax: 0,
      discount: 0,
      total: 0,
      notes: '',
      paymentMethod: 'cash',
      paymentStatus: 'pending'
    });

    console.log('表单已重置');
  };

  // 处理从历史订单编辑时的数据回显，或者清空表单
  useEffect(() => {
    if (currentOrder && currentOrder.id !== orderInfo.id) {
      // 回显客户信息
      setCustomer(currentOrder.customer);

      // 回显地址信息
      setAddress(currentOrder.address);

      // 回显订单信息
      setOrderInfo(currentOrder.orderInfo);

      console.log('已回显历史订单数据:', currentOrder);
    } else if (!currentOrder && orderInfo.id) {
      // 如果当前订单被清空，重置表单
      resetForm();
    }
  }, [currentOrder]);

  // 当订单项变化时重新计算总价
  useEffect(() => {
    const subtotal = calculateOrderSubtotal(orderInfo.items);
    const total = calculateOrderTotal(subtotal, orderInfo.tax, orderInfo.discount);

    setOrderInfo(prev => ({
      ...prev,
      subtotal,
      total
    }));
  }, [orderInfo.items, orderInfo.tax, orderInfo.discount]);

  // 更新当前订单
  useEffect(() => {
    if (selectedTemplate && (customer.name || address.shippingAddress || orderInfo.items.length > 0)) {
      const order: Order = {
        id: orderInfo.id || generateOrderId(),
        customer,
        address,
        orderInfo,
        template: selectedTemplate,
        createdAt: currentOrder?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      if (!currentOrder || JSON.stringify(currentOrder) !== JSON.stringify(order)) {
        setCurrentOrder(order);
      }
    }
  }, [customer, address, orderInfo, selectedTemplate]);

  const addOrderItem = () => {
    const newItem: OrderItem = {
      id: generateItemId(),
      name: '',
      quantity: 1,
      unitPrice: 0,
      totalPrice: 0
    };

    setOrderInfo(prev => ({
      ...prev,
      items: [...prev.items, newItem]
    }));
  };

  const updateOrderItemFromProduct = (index: number, product: CalculatedProduct) => {
    const updatedItems = [...orderInfo.items];
    const currentItem = updatedItems[index];

    // 数量字段应该表示商品的件数，而不是计算出的面积/长度/重量
    // 对于按面积、长度、重量计价的商品，数量默认为1件
    const itemQuantity = product.template.pricingMethod === 'unit' ? product.actualQuantity : (currentItem.quantity || 1);

    updatedItems[index] = {
      ...updatedItems[index],
      name: product.template.name,
      category: product.template.category,
      description: product.template.description,
      quantity: itemQuantity, // 商品件数
      unitPrice: product.template.pricingMethod === 'unit' ? product.unitPrice : product.totalPrice, // 按规格计价时，单价就是总价
      totalPrice: product.template.pricingMethod === 'unit' ? product.totalPrice : product.totalPrice * itemQuantity,
      // 保存完整的商品规格信息
      dimensions: product.dimensions,
      // 保存原始输入的规格数据，用于历史订单编辑时的正确回显
      originalDimensions: product.dimensions ? { ...product.dimensions } : undefined,
      calculatedQuantity: product.calculatedQuantity,
      actualQuantity: product.actualQuantity,
      pricingMethod: product.template.pricingMethod,
      unit: product.template.unit,
      minQuantity: product.template.minQuantity,
      // 模板信息
      templateId: product.template.id,
      templateName: product.template.name
    };

    setOrderInfo(prev => ({
      ...prev,
      items: updatedItems
    }));
  };

  const updateOrderItem = (index: number, updates: Partial<OrderItem>) => {
    const updatedItems = [...orderInfo.items];
    const currentItem = updatedItems[index];
    const updatedItem = { ...currentItem, ...updates };

    // 重新计算总价
    updatedItem.totalPrice = updatedItem.quantity * updatedItem.unitPrice;

    updatedItems[index] = updatedItem;

    setOrderInfo(prev => ({
      ...prev,
      items: updatedItems
    }));
  };

  const removeOrderItem = (index: number) => {
    setOrderInfo(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }));
  };

  return (
    <div className="space-y-6">
      {/* 客户信息 */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div className="flex items-center mb-4">
          <User className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-2" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">客户信息</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              客户姓名 *
            </label>
            <input
              type="text"
              value={customer.name}
              onChange={(e) => setCustomer(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-gray-100"
              placeholder="请输入客户姓名"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              联系电话 *
            </label>
            <input
              type="tel"
              value={customer.phone}
              onChange={(e) => setCustomer(prev => ({ ...prev, phone: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-gray-100"
              placeholder="请输入联系电话"
              required
            />
            {customer.phone && !validatePhone(customer.phone) && (
              <p className="text-red-500 dark:text-red-400 text-xs mt-1">请输入有效的手机号码</p>
            )}
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              邮箱地址
            </label>
            <input
              type="email"
              value={customer.email || ''}
              onChange={(e) => setCustomer(prev => ({ ...prev, email: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-gray-100"
              placeholder="请输入邮箱地址（可选）"
            />
            {customer.email && !validateEmail(customer.email) && (
              <p className="text-red-500 dark:text-red-400 text-xs mt-1">请输入有效的邮箱地址</p>
            )}
          </div>
        </div>
      </div>

      {/* 地址信息 */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div className="flex items-center mb-4">
          <MapPin className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-2" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">地址信息</h3>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              收货地址 *
            </label>
            <textarea
              value={address.shippingAddress}
              onChange={(e) => setAddress(prev => ({ ...prev, shippingAddress: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-gray-100"
              placeholder="请输入详细的收货地址"
              rows={3}
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              账单地址
            </label>
            <textarea
              value={address.billingAddress || ''}
              onChange={(e) => setAddress(prev => ({ ...prev, billingAddress: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-gray-100"
              placeholder="如与收货地址不同，请填写账单地址"
              rows={3}
            />
          </div>
        </div>
      </div>

      {/* 订单信息 */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div className="flex items-center mb-4">
          <FileText className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-2" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">订单信息</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-1 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              订单号
            </label>
            <input
              type="text"
              value={orderInfo.orderNumber}
              onChange={(e) => setOrderInfo(prev => ({ ...prev, orderNumber: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-gray-100"
              placeholder="自动生成"
            />
          </div>
        </div>
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4 mb-6'>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              订单日期
            </label>
            <DatePicker
              value={orderInfo.date}
              onChange={(date) => setOrderInfo(prev => ({ ...prev, date }))}
              placeholder="选择订单日期"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              支付方式
            </label>
            <select
              value={orderInfo.paymentMethod}
              onChange={(e) => setOrderInfo(prev => ({ ...prev, paymentMethod: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-gray-100"
            >
              <option value="cash">现金</option>
              <option value="card">银行卡</option>
              <option value="alipay">支付宝</option>
              <option value="wechat">微信支付</option>
              <option value="transfer">银行转账</option>
            </select>
          </div>
        </div>

        {/* 商品列表 */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-md font-medium text-gray-900 dark:text-gray-100">商品列表</h4>
            <button
              onClick={addOrderItem}
              className="flex items-center px-3 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors"
            >
              <Plus className="w-4 h-4 mr-1" />
              添加商品
            </button>
          </div>

          <div className="space-y-4">
            {orderInfo.items.map((item, index) => (
              <div key={item.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-700">
                <div className="flex items-start justify-between mb-3">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    商品 {index + 1}
                  </h4>
                  <button
                    onClick={() => removeOrderItem(index)}
                    className="p-1 text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 transition-colors"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {/* 商品选择器 */}
                  <div>
                    <ProductSelector
                      onProductSelect={(product) => updateOrderItemFromProduct(index, product)}
                      initialProduct={item.templateId ? {
                        template: {
                          id: item.templateId,
                          name: item.templateName || item.name,
                          category: item.category || '',
                          unitPrice: item.unitPrice,
                          pricingMethod: item.pricingMethod || 'unit',
                          unit: item.unit || '件',
                          minQuantity: item.minQuantity,
                          description: item.description,
                          createdAt: '',
                          updatedAt: ''
                        },
                        dimensions: item.originalDimensions || item.dimensions,
                        calculatedQuantity: item.calculatedQuantity || item.quantity,
                        actualQuantity: item.actualQuantity || item.quantity,
                        unitPrice: item.unitPrice,
                        totalPrice: item.totalPrice
                      } : undefined}
                    />
                  </div>

                  {/* 手动输入选项 */}
                  <div className="space-y-3">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                        或手动输入商品信息
                      </label>
                      <input
                        type="text"
                        value={item.name}
                        onChange={(e) => updateOrderItem(index, { name: e.target.value })}
                        className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-600 dark:text-gray-100"
                        placeholder="商品名称"
                      />
                    </div>

                    <div className="grid grid-cols-3 gap-2">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                          数量
                        </label>
                        <input
                          type="number"
                          value={item.quantity}
                          onChange={(e) => updateOrderItem(index, { quantity: Number(e.target.value) })}
                          className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-600 dark:text-gray-100"
                          placeholder="数量"
                          min="1"
                        />
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                          单价
                        </label>
                        <input
                          type="number"
                          value={item.unitPrice}
                          onChange={(e) => updateOrderItem(index, { unitPrice: Number(e.target.value) })}
                          className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-600 dark:text-gray-100"
                          placeholder="单价"
                          min="0"
                          step="0.01"
                        />
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                          小计
                        </label>
                        <div className="px-2 py-1 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded text-sm font-medium text-blue-900 dark:text-blue-100">
                          ¥{item.totalPrice.toFixed(2)}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {orderInfo.items.length === 0 && (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                暂无商品，点击"添加商品"开始添加
              </div>
            )}
          </div>
        </div>

        {/* 费用计算 */}
        <div className="border-t pt-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                税费
              </label>
              <input
                type="number"
                value={orderInfo.tax}
                onChange={(e) => setOrderInfo(prev => ({ ...prev, tax: Number(e.target.value) }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="0.00"
                min="0"
                step="0.01"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                折扣
              </label>
              <input
                type="number"
                value={orderInfo.discount}
                onChange={(e) => setOrderInfo(prev => ({ ...prev, discount: Number(e.target.value) }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="0.00"
                min="0"
                step="0.01"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                总计
              </label>
              <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-lg font-semibold text-gray-900">
                ¥{orderInfo.total.toFixed(2)}
              </div>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              备注
            </label>
            <textarea
              value={orderInfo.notes}
              onChange={(e) => setOrderInfo(prev => ({ ...prev, notes: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="订单备注信息（可选）"
              rows={3}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
