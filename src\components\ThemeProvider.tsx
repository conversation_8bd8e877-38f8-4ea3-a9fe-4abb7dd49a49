'use client';

import { useEffect, useState } from 'react';
import { useThemeStore } from '@/store/themeStore';

interface ThemeProviderProps {
  children: React.ReactNode;
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const { initializeTheme, loadTheme, theme } = useThemeStore();
  const [isThemeLoaded, setIsThemeLoaded] = useState(false);

  useEffect(() => {
    const initTheme = async () => {
      try {
        // 先加载保存的主题设置
        await loadTheme();
        // 然后应用主题
        initializeTheme();
        setIsThemeLoaded(true);
      } catch (error) {
        console.error('主题初始化失败:', error);
        // 即使失败也要应用默认主题
        initializeTheme();
        setIsThemeLoaded(true);
      }
    };

    initTheme();
  }, []);

  // 当主题变化时重新应用
  useEffect(() => {
    if (isThemeLoaded) {
      initializeTheme();
    }
  }, [theme, isThemeLoaded]);

  // 在主题加载完成前显示一个最小的加载状态，避免闪烁
  if (!isThemeLoaded) {
    return (
      <div className="min-h-screen bg-white dark:bg-gray-900">
        {children}
      </div>
    );
  }

  return <>{children}</>;
}
