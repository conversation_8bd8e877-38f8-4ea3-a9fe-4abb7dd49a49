'use client';

import { useState, useEffect, useRef } from 'react';
import { useProductStore } from '@/store/productStore';
import { ProductTemplate, ProductDimensions, CalculatedProduct } from '@/types/product';
import { Package, Calculator, Info } from 'lucide-react';
import { formatCurrency } from '@/utils/orderUtils';

interface ProductSelectorProps {
  onProductSelect: (product: CalculatedProduct) => void;
  initialProduct?: CalculatedProduct;
}

export function ProductSelector({ onProductSelect, initialProduct }: ProductSelectorProps) {
  const { templates, calculateProduct } = useProductStore();
  const lastCalculatedRef = useRef<CalculatedProduct | null>(null);

  const [selectedTemplate, setSelectedTemplate] = useState<ProductTemplate | null>(
    initialProduct?.template || null
  );
  const [dimensions, setDimensions] = useState<ProductDimensions>(
    initialProduct?.dimensions || {}
  );
  const [calculatedProduct, setCalculatedProduct] = useState<CalculatedProduct | null>(
    initialProduct || null
  );

  // 当模板或尺寸变化时重新计算
  useEffect(() => {
    if (selectedTemplate) {
      const calculated = calculateProduct(selectedTemplate, dimensions);
      setCalculatedProduct(calculated);

      // 只有在计算结果真正改变时才调用回调，避免无限循环
      const hasChanged = !lastCalculatedRef.current ||
        lastCalculatedRef.current.template.id !== calculated.template.id ||
        lastCalculatedRef.current.totalPrice !== calculated.totalPrice ||
        JSON.stringify(lastCalculatedRef.current.dimensions) !== JSON.stringify(calculated.dimensions);

      if (hasChanged) {
        lastCalculatedRef.current = calculated;
        onProductSelect(calculated);
      }
    }
  }, [selectedTemplate, dimensions]);

  // 防止初始化时的空值错误
  useEffect(() => {
    if (initialProduct && !selectedTemplate) {
      setSelectedTemplate(initialProduct.template);
      setDimensions(initialProduct.dimensions || {});
    }
  }, [initialProduct, selectedTemplate]);

  const handleTemplateChange = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    setSelectedTemplate(template || null);

    // 重置尺寸
    setDimensions({});
  };

  const handleDimensionChange = (field: keyof ProductDimensions, value: number) => {
    setDimensions(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const renderDimensionInputs = () => {
    if (!selectedTemplate) return null;

    switch (selectedTemplate.pricingMethod) {
      case 'area':
        return (
          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                长度 (mm)
              </label>
              <input
                type="number"
                value={dimensions.length || ''}
                onChange={(e) => handleDimensionChange('length', Number(e.target.value))}
                className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
                placeholder="长度"
                min="0"
              />
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                宽度 (mm)
              </label>
              <input
                type="number"
                value={dimensions.width || ''}
                onChange={(e) => handleDimensionChange('width', Number(e.target.value))}
                className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
                placeholder="宽度"
                min="0"
              />
            </div>
          </div>
        );

      case 'length':
        return (
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              长度 (mm)
            </label>
            <input
              type="number"
              value={dimensions.length || ''}
              onChange={(e) => handleDimensionChange('length', Number(e.target.value))}
              className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
              placeholder="长度"
              min="0"
            />
          </div>
        );

      case 'weight':
        return (
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              重量 (kg)
            </label>
            <input
              type="number"
              value={dimensions.weight || ''}
              onChange={(e) => handleDimensionChange('weight', Number(e.target.value))}
              className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
              placeholder="重量"
              min="0"
              step="0.01"
            />
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-3">
      {/* 商品模板选择 */}
      <div>
        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
          选择商品模板
        </label>
        <select
          value={selectedTemplate?.id || ''}
          onChange={(e) => handleTemplateChange(e.target.value)}
          className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
        >
          <option value="">请选择商品模板</option>
          {templates.map(template => (
            <option key={template.id} value={template.id}>
              {template.name} - {formatCurrency(template.unitPrice)}/{template.unit}
            </option>
          ))}
        </select>
      </div>

      {/* 尺寸输入 */}
      {selectedTemplate && selectedTemplate.pricingMethod !== 'unit' && (
        <div>
          {renderDimensionInputs()}
        </div>
      )}

      {/* 计算结果显示 */}
      {calculatedProduct && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
          <div className="flex items-center mb-2">
            <Calculator className="w-4 h-4 text-blue-600 dark:text-blue-400 mr-2" />
            <span className="text-sm font-medium text-blue-900 dark:text-blue-100">计算结果</span>
          </div>

          <div className="space-y-1 text-xs">
            {calculatedProduct.template.pricingMethod === 'area' && dimensions.length && dimensions.width && (
              <div className="flex justify-between text-gray-700 dark:text-gray-300">
                <span>计算面积:</span>
                <span>{calculatedProduct.calculatedQuantity.toFixed(3)} 平方米</span>
              </div>
            )}

            {calculatedProduct.template.pricingMethod === 'length' && dimensions.length && (
              <div className="flex justify-between text-gray-700 dark:text-gray-300">
                <span>计算长度:</span>
                <span>{calculatedProduct.calculatedQuantity.toFixed(3)} 米</span>
              </div>
            )}

            <div className="flex justify-between text-gray-700 dark:text-gray-300">
              <span>实际计量:</span>
              <span>{calculatedProduct.actualQuantity.toFixed(3)} {calculatedProduct.template.unit}</span>
            </div>

            <div className="flex justify-between text-gray-700 dark:text-gray-300">
              <span>单价:</span>
              <span>{formatCurrency(calculatedProduct.unitPrice)}</span>
            </div>

            <div className="flex justify-between font-medium text-blue-900 dark:text-blue-100 pt-1 border-t border-blue-200 dark:border-blue-700">
              <span>小计:</span>
              <span>{formatCurrency(calculatedProduct.totalPrice)}</span>
            </div>
          </div>

          {/* 最小计量提示 */}
          {calculatedProduct.template.minQuantity &&
           calculatedProduct.calculatedQuantity < calculatedProduct.template.minQuantity && (
            <div className="mt-2 flex items-start">
              <Info className="w-3 h-3 text-amber-500 mr-1 mt-0.5 flex-shrink-0" />
              <span className="text-xs text-amber-700 dark:text-amber-300">
                不足 {calculatedProduct.template.minQuantity} {calculatedProduct.template.unit}，
                按 {calculatedProduct.template.minQuantity} {calculatedProduct.template.unit} 计算
              </span>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
