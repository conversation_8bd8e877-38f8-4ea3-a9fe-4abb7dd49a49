'use client';

import { useState, useEffect } from 'react';
import { Wifi, WifiOff, Download, RefreshCw } from 'lucide-react';
import { getNetworkStatus, onNetworkStatusChange, isPWA, getPWAAnalytics } from '@/utils/pwa';

export function PWAStatus() {
  const [isOnline, setIsOnline] = useState(true);
  const [isPWAMode, setIsPWAMode] = useState(false);
  const [analytics, setAnalytics] = useState<any>(null);

  useEffect(() => {
    // 初始化状态
    setIsOnline(getNetworkStatus() === 'online');
    setIsPWAMode(isPWA());
    setAnalytics(getPWAAnalytics());

    // 监听网络状态变化
    const unsubscribe = onNetworkStatusChange((status) => {
      setIsOnline(status === 'online');
    });

    return unsubscribe;
  }, []);

  if (!isPWAMode) {
    return null; // 只在 PWA 模式下显示
  }

  return (
    <div className="fixed bottom-4 right-4 z-40">
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-3">
        <div className="flex items-center space-x-2">
          {/* 网络状态指示器 */}
          <div className="flex items-center space-x-1">
            {isOnline ? (
              <Wifi className="w-4 h-4 text-green-500" />
            ) : (
              <WifiOff className="w-4 h-4 text-red-500" />
            )}
            <span className="text-xs text-gray-600 dark:text-gray-400">
              {isOnline ? '在线' : '离线'}
            </span>
          </div>

          {/* PWA 模式指示器 */}
          <div className="flex items-center space-x-1">
            <Download className="w-4 h-4 text-blue-500" />
            <span className="text-xs text-gray-600 dark:text-gray-400">
              已安装
            </span>
          </div>

          {/* 启动次数 */}
          {analytics && analytics.launchCount > 1 && (
            <div className="flex items-center space-x-1">
              <RefreshCw className="w-4 h-4 text-purple-500" />
              <span className="text-xs text-gray-600 dark:text-gray-400">
                {analytics.launchCount}次
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// 网络状态提示组件
export function NetworkStatus() {
  const [isOnline, setIsOnline] = useState(true);
  const [showOfflineMessage, setShowOfflineMessage] = useState(false);

  useEffect(() => {
    setIsOnline(getNetworkStatus() === 'online');

    const unsubscribe = onNetworkStatusChange((status) => {
      const online = status === 'online';
      setIsOnline(online);
      
      if (!online) {
        setShowOfflineMessage(true);
      } else {
        // 重新连接时显示提示
        if (showOfflineMessage) {
          setTimeout(() => setShowOfflineMessage(false), 3000);
        }
      }
    });

    return unsubscribe;
  }, [showOfflineMessage]);

  if (!showOfflineMessage && isOnline) {
    return null;
  }

  return (
    <div className="fixed top-4 left-4 right-4 z-50 max-w-sm mx-auto">
      <div className={`rounded-lg shadow-lg p-4 ${
        isOnline 
          ? 'bg-green-600 text-white' 
          : 'bg-orange-600 text-white'
      }`}>
        <div className="flex items-center space-x-2">
          {isOnline ? (
            <Wifi className="w-5 h-5" />
          ) : (
            <WifiOff className="w-5 h-5" />
          )}
          <div>
            <p className="text-sm font-medium">
              {isOnline ? '网络已恢复' : '网络连接中断'}
            </p>
            <p className="text-xs opacity-90">
              {isOnline 
                ? '您现在可以同步数据了' 
                : '您仍可以离线使用应用'
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
