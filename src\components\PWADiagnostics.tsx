'use client';

import { useState, useEffect } from 'react';
import { AlertCircle, CheckCircle, XCircle, Info } from 'lucide-react';

interface DiagnosticResult {
  name: string;
  status: 'pass' | 'fail' | 'warning' | 'info';
  message: string;
  details?: string;
}

export function PWADiagnostics() {
  const [results, setResults] = useState<DiagnosticResult[]>([]);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // 只在开发环境或特定条件下显示
    const shouldShow = process.env.NODE_ENV === 'development' || 
                      localStorage.getItem('show-pwa-diagnostics') === 'true';
    setIsVisible(shouldShow);

    if (shouldShow) {
      runDiagnostics();
    }
  }, []);

  const runDiagnostics = async () => {
    const diagnostics: DiagnosticResult[] = [];

    // 1. 检查HTTPS
    const isHTTPS = window.location.protocol === 'https:' || 
                   window.location.hostname === 'localhost' ||
                   window.location.hostname === '127.0.0.1';
    diagnostics.push({
      name: 'HTTPS 协议',
      status: isHTTPS ? 'pass' : 'fail',
      message: isHTTPS ? '使用安全协议' : '需要 HTTPS 协议',
      details: isHTTPS ? undefined : 'PWA 需要在 HTTPS 环境下运行（localhost 除外）'
    });

    // 2. 检查 Service Worker
    const swSupported = 'serviceWorker' in navigator;
    diagnostics.push({
      name: 'Service Worker 支持',
      status: swSupported ? 'pass' : 'fail',
      message: swSupported ? '浏览器支持 Service Worker' : '浏览器不支持 Service Worker',
      details: swSupported ? undefined : '当前浏览器不支持 Service Worker'
    });

    // 3. 检查 Service Worker 注册状态
    if (swSupported) {
      try {
        const registration = await navigator.serviceWorker.getRegistration();
        diagnostics.push({
          name: 'Service Worker 注册',
          status: registration ? 'pass' : 'warning',
          message: registration ? 'Service Worker 已注册' : 'Service Worker 未注册',
          details: registration ? `Scope: ${registration.scope}` : '请检查 Service Worker 注册代码'
        });
      } catch (error) {
        diagnostics.push({
          name: 'Service Worker 注册',
          status: 'fail',
          message: 'Service Worker 注册检查失败',
          details: error instanceof Error ? error.message : '未知错误'
        });
      }
    }

    // 4. 检查 Manifest
    try {
      const response = await fetch('/manifest.json');
      const manifest = await response.json();
      
      diagnostics.push({
        name: 'Manifest 文件',
        status: response.ok ? 'pass' : 'fail',
        message: response.ok ? 'Manifest 文件可访问' : 'Manifest 文件无法访问',
        details: response.ok ? `名称: ${manifest.name}` : '请检查 manifest.json 文件'
      });

      // 检查必需的 manifest 字段
      const requiredFields = ['name', 'short_name', 'start_url', 'display', 'icons'];
      const missingFields = requiredFields.filter(field => !manifest[field]);
      
      if (missingFields.length === 0) {
        diagnostics.push({
          name: 'Manifest 必需字段',
          status: 'pass',
          message: '所有必需字段都存在',
        });
      } else {
        diagnostics.push({
          name: 'Manifest 必需字段',
          status: 'fail',
          message: `缺少必需字段: ${missingFields.join(', ')}`,
          details: '请在 manifest.json 中添加缺少的字段'
        });
      }

      // 检查图标
      if (manifest.icons && manifest.icons.length > 0) {
        const hasRequiredSizes = manifest.icons.some((icon: any) => 
          icon.sizes === '192x192' || icon.sizes === '512x512'
        );
        diagnostics.push({
          name: 'Manifest 图标',
          status: hasRequiredSizes ? 'pass' : 'warning',
          message: hasRequiredSizes ? '包含必需尺寸的图标' : '建议添加 192x192 和 512x512 尺寸的图标',
          details: `当前图标数量: ${manifest.icons.length}`
        });
      } else {
        diagnostics.push({
          name: 'Manifest 图标',
          status: 'fail',
          message: '没有定义图标',
          details: '请在 manifest.json 中添加图标配置'
        });
      }
    } catch (error) {
      diagnostics.push({
        name: 'Manifest 文件',
        status: 'fail',
        message: 'Manifest 文件加载失败',
        details: error instanceof Error ? error.message : '未知错误'
      });
    }

    // 5. 检查安装条件
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches ||
                        (window.navigator as any).standalone ||
                        document.referrer.includes('android-app://');
    
    diagnostics.push({
      name: '应用安装状态',
      status: isStandalone ? 'info' : 'info',
      message: isStandalone ? '应用已安装' : '应用未安装',
      details: isStandalone ? '当前运行在独立模式下' : '当前运行在浏览器中'
    });

    // 6. 检查浏览器支持
    const userAgent = navigator.userAgent.toLowerCase();
    const isChrome = /chrome/.test(userAgent) && !/edge/.test(userAgent);
    const isFirefox = /firefox/.test(userAgent);
    const isSafari = /safari/.test(userAgent) && !/chrome/.test(userAgent);
    const isEdge = /edge/.test(userAgent);

    let browserSupport = 'unknown';
    if (isChrome) browserSupport = 'excellent';
    else if (isFirefox || isEdge) browserSupport = 'good';
    else if (isSafari) browserSupport = 'limited';

    diagnostics.push({
      name: '浏览器 PWA 支持',
      status: browserSupport === 'excellent' ? 'pass' : 
              browserSupport === 'good' ? 'warning' : 'info',
      message: `${browserSupport === 'excellent' ? '完全支持' : 
                browserSupport === 'good' ? '良好支持' : 
                browserSupport === 'limited' ? '有限支持' : '未知支持'}`,
      details: `当前浏览器: ${isChrome ? 'Chrome' : isFirefox ? 'Firefox' : 
                           isSafari ? 'Safari' : isEdge ? 'Edge' : '其他'}`
    });

    setResults(diagnostics);
  };

  const getStatusIcon = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'pass':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'fail':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'warning':
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      case 'info':
        return <Info className="w-4 h-4 text-blue-500" />;
    }
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 w-80 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 z-50 max-h-96 overflow-y-auto">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
          PWA 诊断
        </h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          ×
        </button>
      </div>

      <div className="space-y-2">
        {results.map((result, index) => (
          <div key={index} className="flex items-start space-x-2">
            {getStatusIcon(result.status)}
            <div className="flex-1 min-w-0">
              <div className="text-xs font-medium text-gray-900 dark:text-gray-100">
                {result.name}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                {result.message}
              </div>
              {result.details && (
                <div className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                  {result.details}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      <button
        onClick={runDiagnostics}
        className="mt-3 w-full px-3 py-1 bg-blue-600 text-white text-xs font-medium rounded-md hover:bg-blue-700 transition-colors"
      >
        重新检查
      </button>
    </div>
  );
}
