import { create } from 'zustand';
import { ProductTemplate, ProductStore, ProductDimensions, CalculatedProduct } from '@/types/product';
import { dataStore } from '@/utils/indexedDBAdapter';

// 默认商品模板
const defaultTemplates: ProductTemplate[] = [
  {
    id: 'template_1',
    name: '普通纱窗',
    category: '纱窗',
    unitPrice: 120,
    pricingMethod: 'area',
    unit: '平方米',
    minQuantity: 1,
    description: '标准防蚊纱窗，不足1平方米按1平方米计算',
    isDefault: true, // 标识为默认模板
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'template_2',
    name: '金刚网纱窗',
    category: '纱窗',
    unitPrice: 180,
    pricingMethod: 'area',
    unit: '平方米',
    minQuantity: 1,
    description: '加强型金刚网纱窗，防盗防蚊',
    isDefault: true, // 标识为默认模板
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'template_3',
    name: '纱窗配件',
    category: '五金配件',
    unitPrice: 15,
    pricingMethod: 'unit',
    unit: '件',
    description: '纱窗安装配件',
    isDefault: true, // 标识为默认模板
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

function generateTemplateId(): string {
  return `template_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
}

export const useProductStore = create<ProductStore>()((set, get) => ({
  templates: defaultTemplates,
  selectedTemplate: null,

      addTemplate: async (templateData) => {
        const newTemplate: ProductTemplate = {
          ...templateData,
          id: generateTemplateId(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        // 保存到 IndexedDB
        try {
          await dataStore.saveProduct(newTemplate);
        } catch (error) {
          console.error('Failed to save product to IndexedDB:', error);
        }

        set(state => ({
          templates: [...state.templates, newTemplate]
        }));
      },

      updateTemplate: async (id, updates) => {
        const { templates } = get();
        const updatedTemplate = templates.find(t => t.id === id);

        if (updatedTemplate) {
          const finalTemplate = {
            ...updatedTemplate,
            ...updates,
            // 如果是默认模板被编辑，移除默认标识并保存到IndexedDB
            isDefault: updatedTemplate.isDefault ? false : updatedTemplate.isDefault,
            updatedAt: new Date().toISOString()
          };

          // 更新到 IndexedDB
          try {
            await dataStore.saveProduct(finalTemplate);
          } catch (error) {
            console.error('Failed to update product in IndexedDB:', error);
          }
        }

        set(state => ({
          templates: state.templates.map(template =>
            template.id === id
              ? {
                  ...template,
                  ...updates,
                  // 编辑默认模板时移除默认标识
                  isDefault: template.isDefault ? false : template.isDefault,
                  updatedAt: new Date().toISOString()
                }
              : template
          )
        }));
      },

      deleteTemplate: async (id) => {
        const { templates } = get();
        const templateToDelete = templates.find(t => t.id === id);

        // 防止删除默认模板
        if (templateToDelete?.isDefault) {
          console.warn('Cannot delete default template');
          throw new Error('不能删除默认模板');
        }

        // 从 IndexedDB 删除
        try {
          await dataStore.deleteProduct(id);
        } catch (error) {
          console.error('Failed to delete product from IndexedDB:', error);
        }

        set(state => ({
          templates: state.templates.filter(template => template.id !== id),
          selectedTemplate: state.selectedTemplate?.id === id ? null : state.selectedTemplate
        }));
      },

      loadTemplates: async () => {
        try {
          const savedTemplates = await dataStore.getAllProducts();

          // 合并默认模板和保存的模板
          const defaultTemplateIds = defaultTemplates.map(t => t.id);
          const savedTemplateIds = savedTemplates.map(t => t.id);

          // 找出缺失的默认模板
          const missingDefaultTemplates = defaultTemplates.filter(
            defaultTemplate => !savedTemplateIds.includes(defaultTemplate.id)
          );

          // 找出非默认的保存模板
          const userTemplates = savedTemplates.filter(
            savedTemplate => !defaultTemplateIds.includes(savedTemplate.id)
          );

          // 找出已修改的默认模板（isDefault为false的原默认模板）
          const modifiedDefaultTemplates = savedTemplates.filter(
            savedTemplate => defaultTemplateIds.includes(savedTemplate.id)
          );

          // 合并所有模板：缺失的默认模板 + 已修改的默认模板 + 用户模板
          const allTemplates = [
            ...missingDefaultTemplates,
            ...modifiedDefaultTemplates,
            ...userTemplates
          ];

          set({ templates: allTemplates.length > 0 ? allTemplates : defaultTemplates });
        } catch (error) {
          console.error('Failed to load products from IndexedDB:', error);
          set({ templates: defaultTemplates });
        }
      },

      setSelectedTemplate: (template) => {
        set({ selectedTemplate: template });
      },

      getTemplatesByCategory: (category) => {
        const { templates } = get();
        return templates.filter(template => template.category === category);
      },

      calculateProduct: (template, dimensions) => {
        let calculatedQuantity = 1;
        let actualQuantity = 1;

        switch (template.pricingMethod) {
          case 'area':
            if (dimensions?.length && dimensions?.width) {
              calculatedQuantity = (dimensions.length / 1000) * (dimensions.width / 1000); // 转换为平方米
              actualQuantity = Math.max(calculatedQuantity, template.minQuantity || 1);
            }
            break;

          case 'length':
            if (dimensions?.length) {
              calculatedQuantity = dimensions.length / 1000; // 转换为米
              actualQuantity = Math.max(calculatedQuantity, template.minQuantity || 1);
            }
            break;

          case 'weight':
            if (dimensions?.weight) {
              calculatedQuantity = dimensions.weight;
              actualQuantity = Math.max(calculatedQuantity, template.minQuantity || 1);
            }
            break;

          case 'unit':
          default:
            calculatedQuantity = 1;
            actualQuantity = 1;
            break;
        }

        const totalPrice = actualQuantity * template.unitPrice;

        return {
          template,
          dimensions,
          calculatedQuantity,
          actualQuantity,
          unitPrice: template.unitPrice,
          totalPrice
        };
      }
    }));
