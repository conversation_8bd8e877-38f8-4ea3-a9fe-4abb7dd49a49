import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { Order, ExportOptions } from '@/types/order';
import { BusinessInfo } from '@/types/business';
import { formatCurrency, formatDate } from './orderUtils';

// 生成收据 HTML 内容
function generateReceiptHTML(order: Order, businessInfo?: BusinessInfo | null): string {
  const { customer, address, orderInfo, template } = order;
  const styles = template.styles;

  return `
    <div style="
      font-family: ${styles.fontFamily};
      font-size: ${styles.fontSize};
      color: ${styles.primaryColor};
      background-color: white;
      padding: 40px;
      max-width: 800px;
      margin: 0 auto;
      line-height: 1.6;
      width: 210mm;
      min-height: 297mm;
      box-sizing: border-box;
    ">

      <!-- 头部 -->
      <div style="
        text-align: center;
        border-bottom: 2px solid ${styles.primaryColor};
        padding-bottom: 20px;
        margin-bottom: 30px;
      ">
        <h1 style="
          margin: 0;
          font-size: 28px;
          font-weight: bold;
          color: ${styles.primaryColor};
        ">订单收据</h1>
        <p style="
          margin: 10px 0 0 0;
          font-size: 16px;
          color: #666;
        ">ORDER RECEIPT</p>
      </div>

      <!-- 订单信息 -->
      <div style="
        display: flex;
        justify-content: space-between;
        margin-bottom: 30px;
        flex-wrap: wrap;
      ">
        <div style="flex: 1; min-width: 200px;">
          <h3 style="
            margin: 0 0 10px 0;
            font-size: 16px;
            color: ${styles.primaryColor};
          ">订单信息</h3>
          <p style="margin: 5px 0;"><strong>订单号:</strong> ${orderInfo.orderNumber}</p>
          <p style="margin: 5px 0;"><strong>日期:</strong> ${formatDate(orderInfo.date, 'yyyy年MM月dd日')}</p>
          <p style="margin: 5px 0;"><strong>支付方式:</strong> ${getPaymentMethodText(orderInfo.paymentMethod)}</p>
        </div>

        <div style="flex: 1; min-width: 200px;">
          <h3 style="
            margin: 0 0 10px 0;
            font-size: 16px;
            color: ${styles.primaryColor};
          ">客户信息</h3>
          <p style="margin: 5px 0;"><strong>姓名:</strong> ${customer.name}</p>
          <p style="margin: 5px 0;"><strong>电话:</strong> ${customer.phone}</p>
          ${customer.email ? `<p style="margin: 5px 0;"><strong>邮箱:</strong> ${customer.email}</p>` : ''}
        </div>
      </div>

      <!-- 地址信息 -->
      <div style="margin-bottom: 30px;">
        <h3 style="
          margin: 0 0 10px 0;
          font-size: 16px;
          color: ${styles.primaryColor};
        ">收货地址</h3>
        <p style="
          margin: 0;
          padding: 10px;
          background-color: ${styles.secondaryColor};
          border-radius: 4px;
        ">${address.shippingAddress}</p>
        ${address.billingAddress ? `
          <h3 style="
            margin: 15px 0 10px 0;
            font-size: 16px;
            color: ${styles.primaryColor};
          ">账单地址</h3>
          <p style="
            margin: 0;
            padding: 10px;
            background-color: ${styles.secondaryColor};
            border-radius: 4px;
          ">${address.billingAddress}</p>
        ` : ''}
      </div>

      <!-- 商品列表 -->
      <div style="margin-bottom: 30px;">
        <h3 style="
          margin: 0 0 15px 0;
          font-size: 16px;
          color: ${styles.primaryColor};
        ">商品明细</h3>

        <table style="
          width: 100%;
          border-collapse: collapse;
          border: 1px solid #ddd;
          font-size: 12px;
        ">
          <thead>
            <tr style="background-color: ${styles.primaryColor}; color: white;">
              <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">商品名称</th>
              <th style="padding: 8px; text-align: center; border: 1px solid #ddd;">规格参数</th>
              <th style="padding: 8px; text-align: center; border: 1px solid #ddd;">数量</th>
              <th style="padding: 8px; text-align: right; border: 1px solid #ddd;">单价</th>
              <th style="padding: 8px; text-align: right; border: 1px solid #ddd;">小计</th>
            </tr>
          </thead>
          <tbody>
            ${orderInfo.items.map((item, index) => {
              // 获取规格参数显示文本
              const getSpecText = () => {
                const dimensions = item.originalDimensions || item.dimensions;
                if (!dimensions) return '-';

                switch (item.pricingMethod) {
                  case 'area':
                    if (dimensions.length && dimensions.width) {
                      const area = (dimensions.length / 1000) * (dimensions.width / 1000);
                      return `${dimensions.length}×${dimensions.width}mm<br/>(${area.toFixed(2)}㎡)`;
                    }
                    break;
                  case 'length':
                    if (dimensions.length) {
                      return `长度: ${dimensions.length}mm<br/>(${(dimensions.length / 1000).toFixed(2)}m)`;
                    }
                    break;
                  case 'weight':
                    if (dimensions.weight) {
                      return `重量: ${dimensions.weight}kg`;
                    }
                    break;
                  default:
                    return '-';
                }
                return '-';
              };

              return `
                <tr style="background-color: ${index % 2 === 0 ? 'white' : styles.secondaryColor};">
                  <td style="padding: 8px; border: 1px solid #ddd;">
                    <div style="font-weight: bold;">${item.name}</div>
                    ${item.description ? `<div style="font-size: 10px; color: #666; margin-top: 2px;">${item.description}</div>` : ''}
                  </td>
                  <td style="padding: 8px; text-align: center; border: 1px solid #ddd; font-size: 10px;">
                    ${getSpecText()}
                  </td>
                  <td style="padding: 8px; text-align: center; border: 1px solid #ddd;">
                    ${item.quantity} ${item.unit === '件' ? '件' : ''}
                  </td>
                  <td style="padding: 8px; text-align: right; border: 1px solid #ddd;">
                    ${formatCurrency(item.unitPrice)}/${item.unit}
                  </td>
                  <td style="padding: 8px; text-align: right; border: 1px solid #ddd;">
                    ${formatCurrency(item.totalPrice)}
                  </td>
                </tr>
              `;
            }).join('')}
          </tbody>
        </table>
      </div>

      <!-- 费用汇总 */
      <div style="
        border-top: 2px solid ${styles.primaryColor};
        padding-top: 20px;
        margin-bottom: 30px;
      ">
        <div style="text-align: right;">
          <div style="margin-bottom: 10px;">
            <span style="display: inline-block; width: 100px;">小计:</span>
            <span style="font-weight: bold;">${formatCurrency(orderInfo.subtotal)}</span>
          </div>
          ${orderInfo.tax ? `
            <div style="margin-bottom: 10px;">
              <span style="display: inline-block; width: 100px;">税费:</span>
              <span>${formatCurrency(orderInfo.tax)}</span>
            </div>
          ` : ''}
          ${orderInfo.discount ? `
            <div style="margin-bottom: 10px;">
              <span style="display: inline-block; width: 100px;">折扣:</span>
              <span>-${formatCurrency(orderInfo.discount)}</span>
            </div>
          ` : ''}
          <div style="
            font-size: 18px;
            font-weight: bold;
            color: ${styles.primaryColor};
            border-top: 1px solid #ddd;
            padding-top: 10px;
            margin-top: 10px;
          ">
            <span style="display: inline-block; width: 100px;">总计:</span>
            <span>${formatCurrency(orderInfo.total)}</span>
          </div>
        </div>
      </div>

      <!-- 备注 -->
      ${orderInfo.notes ? `
        <div style="margin-bottom: 30px;">
          <h3 style="
            margin: 0 0 10px 0;
            font-size: 16px;
            color: ${styles.primaryColor};
          ">备注</h3>
          <p style="
            margin: 0;
            padding: 10px;
            background-color: ${styles.secondaryColor};
            border-radius: 4px;
          ">${orderInfo.notes}</p>
        </div>
      ` : ''}

      ${businessInfo ? `
        <!-- 商业信息页脚 -->
        <div style="
          border-top: 2px solid ${styles.primaryColor};
          padding-top: 16px;
          margin-top: 24px;
          margin-bottom: 16px;
        ">
          <div style="
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            background-color: ${styles.secondaryColor};
            padding: 16px;
            border-radius: 8px;
          ">
            <div style="flex: 1;">
              <div style="display: flex; align-items: center; margin-bottom: 8px;">
                ${businessInfo.logo ? `
                  <img src="${businessInfo.logo}" alt="企业Logo" style="
                    height: 32px;
                    margin-right: 12px;
                    object-fit: contain;
                  " />
                ` : ''}
                <h3 style="
                  margin: 0;
                  font-size: 16px;
                  font-weight: bold;
                  color: ${styles.primaryColor};
                ">${businessInfo.companyName}</h3>
              </div>

              <div style="font-size: 11px; color: #6b7280; line-height: 1.4;">
                ${businessInfo.phone ? `<div style="margin-bottom: 2px;"><strong>联系电话:</strong> ${businessInfo.phone}</div>` : ''}
                ${businessInfo.wechat ? `<div style="margin-bottom: 2px;"><strong>微信号:</strong> ${businessInfo.wechat}</div>` : ''}
                ${businessInfo.address ? `<div style="margin-bottom: 2px;"><strong>地址:</strong> ${businessInfo.address}</div>` : ''}
                ${businessInfo.email ? `<div style="margin-bottom: 2px;"><strong>邮箱:</strong> ${businessInfo.email}</div>` : ''}
                ${businessInfo.website ? `<div><strong>网站:</strong> ${businessInfo.website}</div>` : ''}
              </div>
            </div>

            ${businessInfo.qrCode ? `
              <div style="margin-left: 20px; text-align: center;">
                <img src="${businessInfo.qrCode}" alt="企业二维码" style="
                  width: 80px;
                  height: 80px;
                  object-fit: contain;
                  border: 1px solid #ddd;
                  border-radius: 4px;
                " />
                <div style="font-size: 9px; color: #666; margin-top: 4px;">
                  扫码联系我们
                </div>
              </div>
            ` : ''}
          </div>
        </div>
      ` : ''}

      <!-- 页脚 -->
      <div style="
        text-align: center;
        border-top: 1px solid #ddd;
        padding-top: 12px;
        color: #666;
        font-size: 10px;
      ">
        <p style="margin: 0;">感谢您的购买！</p>
        <p style="margin: 4px 0 0 0;">如有问题，请联系客服</p>
      </div>
    </div>
  `;
}

function getPaymentMethodText(method?: string): string {
  const methods: Record<string, string> = {
    cash: '现金',
    card: '银行卡',
    alipay: '支付宝',
    wechat: '微信支付',
    transfer: '银行转账'
  };
  return methods[method || 'cash'] || '现金';
}

// 创建临时 DOM 元素用于渲染
function createTempElement(html: string): HTMLElement {
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;
  tempDiv.style.position = 'absolute';
  tempDiv.style.left = '-9999px';
  tempDiv.style.top = '-9999px';
  document.body.appendChild(tempDiv);
  return tempDiv;
}

// 导出为 PDF
export async function exportToPDF(order: Order, options: ExportOptions, businessInfo?: any): Promise<void> {
  const html = generateReceiptHTML(order, businessInfo);
  const tempElement = createTempElement(html);

  try {
    const canvas = await html2canvas(tempElement, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff'
    });

    const imgData = canvas.toDataURL('image/png');

    // 创建 PDF
    const pdf = new jsPDF({
      orientation: options.orientation || 'portrait',
      unit: 'mm',
      format: options.size || 'a4'
    });

    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = pdf.internal.pageSize.getHeight();
    const imgWidth = canvas.width;
    const imgHeight = canvas.height;

    // 计算缩放比例
    const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);
    const scaledWidth = imgWidth * ratio;
    const scaledHeight = imgHeight * ratio;

    // 居中显示
    const x = (pdfWidth - scaledWidth) / 2;
    const y = (pdfHeight - scaledHeight) / 2;

    pdf.addImage(imgData, 'PNG', x, y, scaledWidth, scaledHeight);

    // 下载文件
    const fileName = `订单收据_${order.orderInfo.orderNumber}_${formatDate(new Date(), 'yyyyMMdd')}.pdf`;
    pdf.save(fileName);

  } finally {
    document.body.removeChild(tempElement);
  }
}

// 导出为 PNG
export async function exportToPNG(order: Order, options: ExportOptions, businessInfo?: any): Promise<void> {
  const html = generateReceiptHTML(order, businessInfo);
  const tempElement = createTempElement(html);

  try {
    const canvas = await html2canvas(tempElement, {
      scale: options.quality || 1,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff'
    });

    // 创建下载链接
    const link = document.createElement('a');
    link.download = `订单收据_${order.orderInfo.orderNumber}_${formatDate(new Date(), 'yyyyMMdd')}.png`;
    link.href = canvas.toDataURL('image/png');

    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

  } finally {
    document.body.removeChild(tempElement);
  }
}
