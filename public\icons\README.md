# PWA 图标说明

此目录包含 PWA 应用所需的各种尺寸图标。

## 图标尺寸要求

- icon-72x72.png - 72x72 像素
- icon-96x96.png - 96x96 像素  
- icon-128x128.png - 128x128 像素
- icon-144x144.png - 144x144 像素
- icon-152x152.png - 152x152 像素
- icon-192x192.png - 192x192 像素
- icon-384x384.png - 384x384 像素
- icon-512x512.png - 512x512 像素

## 设计要求

- 图标应该是正方形
- 建议使用简洁的设计
- 确保在小尺寸下仍然清晰可见
- 支持透明背景
- 建议使用品牌色彩

## 生成工具

可以使用以下工具生成不同尺寸的图标：
- PWA Builder (https://www.pwabuilder.com/)
- Favicon Generator (https://realfavicongenerator.net/)
- 或者使用设计软件手动创建

## 注意事项

- 所有图标都应该放在 public/icons/ 目录下
- 文件名必须与 manifest.json 中的配置一致
- 建议同时提供 maskable 和 any 用途的图标
