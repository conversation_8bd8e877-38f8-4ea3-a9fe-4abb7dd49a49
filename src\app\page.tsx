'use client';

import { OrderForm } from '@/components/OrderForm';
import { TemplateSelector } from '@/components/TemplateSelector';
import { ExportButtons } from '@/components/ExportButtons';
import { ReceiptPreview } from '@/components/ReceiptPreview';
import { useOrderStore } from '@/store/orderStore';

export default function Home() {
  const { currentOrder, clearCurrentOrder } = useOrderStore();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 sm:text-4xl">
            订单收据开单工具
          </h1>
          <p className="mt-2 text-lg text-gray-600 dark:text-gray-400">
            快速创建专业的订单收据和发票
          </p>

          {/* 编辑状态提示 */}
          {currentOrder && (
            <div className="mt-4 inline-flex items-center px-4 py-2 bg-blue-100 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <span className="text-blue-800 dark:text-blue-200 text-sm">
                正在编辑订单: {currentOrder.orderInfo.orderNumber}
              </span>
              <button
                onClick={clearCurrentOrder}
                className="ml-3 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm underline"
              >
                创建新订单
              </button>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 左侧：表单区域 */}
          <div className="space-y-6">
            <OrderForm />

          </div>

          {/* 右侧：预览区域 */}
          <div className="lg:sticky lg:top-8 space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                收据预览
              </h3>
              <div id="receipt-preview" className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                <ReceiptPreview />
              </div>
            </div>
            <TemplateSelector />
            <ExportButtons />
          </div>
        </div>
      </div>
    </div>
  );
}
