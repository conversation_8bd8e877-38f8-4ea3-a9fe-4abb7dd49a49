import { format } from 'date-fns';
import { OrderItem } from '@/types/order';

export function generateOrderId(): string {
  return `order_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

export function generateOrderNumber(): string {
  const date = format(new Date(), 'yyyyMMdd');
  const random = Math.random().toString(36).substring(2, 6).toUpperCase();
  return `ORD${date}${random}`;
}

export function calculateItemTotal(item: Partial<OrderItem>): number {
  const quantity = item.quantity || 0;
  const unitPrice = item.unitPrice || 0;
  return quantity * unitPrice;
}

export function calculateOrderSubtotal(items: OrderItem[]): number {
  return items.reduce((sum, item) => sum + item.totalPrice, 0);
}

export function calculateOrderTotal(
  subtotal: number,
  tax: number = 0,
  discount: number = 0
): number {
  return subtotal + tax - discount;
}

export function formatCurrency(amount: number, currency: string = '¥'): string {
  return `${currency}${amount.toFixed(2)}`;
}

export function formatDate(date: string | Date, formatStr: string = 'yyyy-MM-dd'): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return format(dateObj, formatStr);
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function validatePhone(phone: string): boolean {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone.replace(/\s|-/g, ''));
}

export function generateItemId(): string {
  return `item_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
}

export function exportToLocalStorage(key: string, data: any): void {
  try {
    localStorage.setItem(key, JSON.stringify(data));
  } catch (error) {
    console.error('Failed to save to localStorage:', error);
  }
}

export function importFromLocalStorage<T>(key: string, defaultValue: T): T {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.error('Failed to load from localStorage:', error);
    return defaultValue;
  }
}
