export interface CustomerInfo {
  name: string;
  phone: string;
  email?: string;
}

export interface AddressInfo {
  shippingAddress: string;
  billingAddress?: string;
}

export interface ProductDimensions {
  length?: number;
  width?: number;
  height?: number;
  weight?: number;
}

export interface OrderItem {
  id: string;
  name: string;
  category?: string;
  description?: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  // 商品规格信息 - 用于存储订单中商品的具体规格参数
  dimensions?: ProductDimensions;
  // 计算相关字段
  calculatedQuantity?: number; // 根据规格计算出的数量
  actualQuantity?: number; // 实际计量数量（考虑最小计量）
  // 计价信息
  pricingMethod?: 'area' | 'length' | 'weight' | 'unit';
  unit?: string; // 计价单位
  minQuantity?: number; // 最小计量
  // 模板信息
  templateId?: string;
  templateName?: string;
  // 历史订单编辑时需要保留的原始规格数据
  originalDimensions?: ProductDimensions; // 保存原始输入的规格数据
}

export interface OrderInfo {
  id: string;
  orderNumber: string;
  date: string;
  items: OrderItem[];
  subtotal: number;
  tax?: number;
  discount?: number;
  total: number;
  notes?: string;
  paymentMethod?: string;
  paymentStatus?: 'pending' | 'paid' | 'cancelled';
}

export interface Order {
  id: string;
  customer: CustomerInfo;
  address: AddressInfo;
  orderInfo: OrderInfo;
  template: ReceiptTemplate;
  userId?: string; // 关联的用户ID，支持云端同步
  createdAt: string;
  updatedAt: string;
}

export interface ReceiptTemplate {
  id: string;
  name: string;
  description: string;
  preview: string;
  styles: {
    primaryColor: string;
    secondaryColor: string;
    fontFamily: string;
    fontSize: string;
    layout: 'modern' | 'classic' | 'minimal';
  };
}

export interface ExportOptions {
  format: 'pdf' | 'png';
  quality?: number;
  size?: 'a4' | 'letter' | 'custom';
  orientation?: 'portrait' | 'landscape';
}

export interface OrderStore {
  currentOrder: Order | null;
  orders: Order[];
  templates: ReceiptTemplate[];
  selectedTemplate: ReceiptTemplate | null;
  isCloudSyncEnabled: boolean;
  isSyncing: boolean;
  lastSyncAt: string | null;
  syncError: string | null;

  // Actions
  setCurrentOrder: (order: Order) => void;
  updateCurrentOrder: (updates: Partial<Order>) => void;
  saveOrder: (order: Order) => Promise<void>;
  deleteOrder: (orderId: string) => Promise<void>;
  setSelectedTemplate: (template: ReceiptTemplate) => void;
  loadOrders: () => void;
  clearCurrentOrder: () => void;

  // Cloud sync actions
  syncToCloud: () => Promise<void>;
  syncFromCloud: () => Promise<void>;
  enableCloudSync: () => void;
  disableCloudSync: () => void;
  clearSyncError: () => void;
}
