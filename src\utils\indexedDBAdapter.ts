import { StateStorage } from 'zustand/middleware';
import { dbManager, DB_CONFIG } from './indexedDB';

// IndexedDB 存储适配器
export class IndexedDBAdapter implements StateStorage {
  private storeName: string;

  constructor(storeName: string) {
    this.storeName = storeName;
  }

  async getItem(name: string): Promise<string | null> {
    try {
      // 由于settings表已被移除，使用localStorage作为fallback
      const key = `${this.storeName}_${name}`;
      const data = localStorage.getItem(key);
      return data;
    } catch (error) {
      console.error(`Failed to get item ${name}:`, error);
      return null;
    }
  }

  async setItem(name: string, value: string): Promise<void> {
    try {
      // 由于settings表已被移除，使用localStorage作为fallback
      const key = `${this.storeName}_${name}`;
      localStorage.setItem(key, value);
    } catch (error) {
      console.error(`Failed to set item ${name}:`, error);
    }
  }

  async removeItem(name: string): Promise<void> {
    try {
      // 由于settings表已被移除，使用localStorage作为fallback
      const key = `${this.storeName}_${name}`;
      localStorage.removeItem(key);
    } catch (error) {
      console.error(`Failed to remove item ${name}:`, error);
    }
  }
}

// 创建专用的数据存储类
export class OrderDataStore {
  private currentUserId: string | null = null;

  async init(): Promise<void> {
    await dbManager.init();
  }

  // 设置当前用户ID
  setCurrentUserId(userId: string | null): void {
    this.currentUserId = userId;
  }

  // 获取当前用户ID
  getCurrentUserId(): string | null {
    return this.currentUserId;
  }

  // 订单相关方法
  async saveOrder(order: any): Promise<void> {
    const orderWithUser = {
      ...order,
      userId: this.currentUserId || 'anonymous'
    };
    await dbManager.put('orders', orderWithUser);
  }

  async getOrder(id: string): Promise<any> {
    return await dbManager.get('orders', id);
  }

  async getAllOrders(): Promise<any[]> {
    try {
      if (this.currentUserId) {
        return await dbManager.getByIndex('orders', 'by_user', this.currentUserId);
      }
      return await dbManager.getByIndex('orders', 'by_user', 'anonymous');
    } catch (error) {
      console.warn('Failed to get orders by user index, falling back to getAll:', error);
      // 如果索引不存在，回退到获取所有订单并过滤
      const allOrders = await dbManager.getAll('orders');
      const userId = this.currentUserId || 'anonymous';
      return allOrders.filter((order: any) => (order.userId || 'anonymous') === userId);
    }
  }

  async deleteOrder(id: string): Promise<void> {
    await dbManager.delete('orders', id);
  }

  async getOrdersByCustomer(customerName: string): Promise<any[]> {
    const allOrders = await this.getAllOrders();
    return allOrders.filter((order: any) => order.customer?.name === customerName);
  }

  // 商品相关方法
  async saveProduct(product: any): Promise<void> {
    const productWithUser = {
      ...product,
      userId: this.currentUserId || 'anonymous'
    };
    await dbManager.put('products', productWithUser);
  }

  async getProduct(id: string): Promise<any> {
    return await dbManager.get('products', id);
  }

  async getAllProducts(): Promise<any[]> {
    try {
      if (this.currentUserId) {
        return await dbManager.getByIndex('products', 'by_user', this.currentUserId);
      }
      return await dbManager.getByIndex('products', 'by_user', 'anonymous');
    } catch (error) {
      console.warn('Failed to get products by user index, falling back to getAll:', error);
      // 如果索引不存在，回退到获取所有产品并过滤
      const allProducts = await dbManager.getAll('products');
      const userId = this.currentUserId || 'anonymous';
      return allProducts.filter((product: any) => (product.userId || 'anonymous') === userId);
    }
  }

  async deleteProduct(id: string): Promise<void> {
    await dbManager.delete('products', id);
  }

  async getProductsByCategory(category: string): Promise<any[]> {
    const allProducts = await this.getAllProducts();
    return allProducts.filter((product: any) => product.category === category);
  }

  // 商业信息相关方法
  async saveBusiness(business: any): Promise<void> {
    const businessWithUser = {
      ...business,
      userId: this.currentUserId || 'anonymous'
    };
    await dbManager.put('business', businessWithUser);
  }

  async getBusiness(id: string): Promise<any> {
    return await dbManager.get('business', id);
  }

  async getAllBusiness(): Promise<any[]> {
    try {
      if (this.currentUserId) {
        return await dbManager.getByIndex('business', 'by_user', this.currentUserId);
      }
      return await dbManager.getByIndex('business', 'by_user', 'anonymous');
    } catch (error) {
      console.warn('Failed to get business by user index, falling back to getAll:', error);
      // 如果索引不存在，回退到获取所有商业信息并过滤
      const allBusiness = await dbManager.getAll('business');
      const userId = this.currentUserId || 'anonymous';
      return allBusiness.filter((business: any) => (business.userId || 'anonymous') === userId);
    }
  }

  // 用户相关方法
  async saveUser(user: any): Promise<void> {
    await dbManager.put('users', user);
  }

  async getUser(id: string): Promise<any> {
    return await dbManager.get('users', id);
  }

  async getUserByEmail(email: string): Promise<any> {
    const users = await dbManager.getByIndex('users', 'by_email', email);
    return users[0] || null;
  }

  async getAllUsers(): Promise<any[]> {
    return await dbManager.getAll('users');
  }

  async deleteUser(id: string): Promise<void> {
    await dbManager.delete('users', id);
  }



  // 统计方法
  async getOrderCount(): Promise<number> {
    return await dbManager.count('orders');
  }

  async getProductCount(): Promise<number> {
    return await dbManager.count('products');
  }

  async getUserCount(): Promise<number> {
    return await dbManager.count('users');
  }

  // 清理方法
  async clearAllOrders(): Promise<void> {
    await dbManager.clear('orders');
  }

  async clearAllProducts(): Promise<void> {
    await dbManager.clear('products');
  }

  async clearAllUsers(): Promise<void> {
    await dbManager.clear('users');
  }

  // 设置相关方法（现在使用 localStorage）
  async saveSetting(key: string, value: any): Promise<void> {
    try {
      localStorage.setItem(`setting_${key}`, JSON.stringify({
        value,
        timestamp: new Date().toISOString()
      }));
    } catch (error) {
      console.error(`Failed to save setting ${key}:`, error);
      throw error;
    }
  }

  async getSetting(key: string): Promise<any> {
    try {
      const data = localStorage.getItem(`setting_${key}`);
      if (data) {
        const parsed = JSON.parse(data);
        return parsed.value;
      }
      return null;
    } catch (error) {
      console.error(`Failed to get setting ${key}:`, error);
      return null;
    }
  }

  async deleteSetting(key: string): Promise<void> {
    try {
      localStorage.removeItem(`setting_${key}`);
    } catch (error) {
      console.error(`Failed to delete setting ${key}:`, error);
      throw error;
    }
  }

  // 清理所有数据
  async clearAllData(): Promise<void> {
    await dbManager.init();

    const stores = ['orders', 'products', 'business', 'users'];

    for (const storeName of stores) {
      try {
        const allData = await dbManager.getAll(storeName);
        for (const item of allData) {
          await dbManager.delete(storeName, (item as any).id || (item as any).key);
        }
      } catch (error) {
        console.error(`Failed to clear ${storeName}:`, error);
      }
    }
  }

  // 重置数据库（删除并重新创建）
  async resetDatabase(): Promise<void> {
    try {
      // 关闭当前数据库连接
      dbManager.close();

      // 删除数据库
      await new Promise<void>((resolve, reject) => {
        const deleteRequest = indexedDB.deleteDatabase(DB_CONFIG.name);
        deleteRequest.onsuccess = () => resolve();
        deleteRequest.onerror = () => reject(new Error('Failed to delete database'));
        deleteRequest.onblocked = () => {
          console.warn('Database deletion blocked, trying to force close connections');
          setTimeout(() => resolve(), 1000);
        };
      });

      // 重新初始化数据库
      await dbManager.init();
      console.log('Database reset successfully');
    } catch (error) {
      console.error('Failed to reset database:', error);
      throw error;
    }
  }
}

// 创建全局数据存储实例
export const dataStore = new OrderDataStore();
