import type { Metadata, Viewport } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Navigation } from "@/components/Navigation";
import { ThemeProvider } from "@/components/ThemeProvider";
import { AppInitializer } from "@/components/AppInitializer";
import { PWAInstaller, PWAUpdater } from "@/components/PWAInstaller";
import { PWAStatus, NetworkStatus } from "@/components/PWAStatus";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "订单收据开单工具",
  description: "专业的订单收据和发票生成工具，支持多种模板和导出格式",
  keywords: ["订单", "收据", "发票", "开单工具", "商务", "财务"],
  authors: [{ name: "订单收据工具团队" }],
  creator: "订单收据工具",
  publisher: "订单收据工具",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "收据工具",
  },
  openGraph: {
    type: "website",
    siteName: "订单收据开单工具",
    title: "订单收据开单工具",
    description: "专业的订单收据和发票生成工具",
  },
  twitter: {
    card: "summary",
    title: "订单收据开单工具",
    description: "专业的订单收据和发票生成工具",
  },
  other: {
    "mobile-web-app-capable": "yes",
    "apple-mobile-web-app-capable": "yes",
    "apple-mobile-web-app-status-bar-style": "default",
    "apple-mobile-web-app-title": "收据工具",
    "application-name": "收据工具",
    "msapplication-TileColor": "#3B82F6",
    "theme-color": "#3B82F6",
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#3B82F6" },
    { media: "(prefers-color-scheme: dark)", color: "#1F2937" },
  ],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
        <meta name="theme-color" content="#3B82F6" />
      </head>
      <body className={`${inter.variable} font-sans antialiased`}>
        <ThemeProvider>
          <AppInitializer>
            <Navigation />
            <main>{children}</main>
            <PWAInstaller />
            <PWAUpdater />
            <PWAStatus />
            <NetworkStatus />
          </AppInitializer>
        </ThemeProvider>
      </body>
    </html>
  );
}
