/**
 * 统一的存储管理器
 * 规范 IndexedDB、localStorage、sessionStorage、cookies 的使用
 */

// 存储类型枚举
export enum StorageType {
  INDEXED_DB = 'indexeddb',
  LOCAL_STORAGE = 'localStorage',
  SESSION_STORAGE = 'sessionStorage',
  COOKIES = 'cookies'
}

// 存储配置
export interface StorageConfig {
  type: StorageType;
  key: string;
  expiry?: number; // 过期时间（毫秒）
  encrypt?: boolean; // 是否加密
}

// 数据类型定义
export interface StorageData {
  value: any;
  timestamp: number;
  expiry?: number;
}

/**
 * 存储管理器类
 */
export class StorageManager {
  private static instance: StorageManager;

  static getInstance(): StorageManager {
    if (!StorageManager.instance) {
      StorageManager.instance = new StorageManager();
    }
    return StorageManager.instance;
  }

  /**
   * 根据数据类型选择合适的存储方式
   */
  private getStorageType(dataType: string): StorageType {
    switch (dataType) {
      // 大量数据和业务核心数据使用 IndexedDB
      case 'orders':
      case 'products':
      case 'business':
      case 'users':
        return StorageType.INDEXED_DB;
      
      // 用户设置和偏好使用 localStorage（持久化）
      case 'theme':
      case 'language':
      case 'userPreferences':
      case 'appSettings':
        return StorageType.LOCAL_STORAGE;
      
      // 会话相关数据使用 sessionStorage
      case 'currentSession':
      case 'tempData':
      case 'formDraft':
        return StorageType.SESSION_STORAGE;
      
      // 认证相关使用 cookies（支持 httpOnly）
      case 'authToken':
      case 'refreshToken':
        return StorageType.COOKIES;
      
      default:
        return StorageType.LOCAL_STORAGE;
    }
  }

  /**
   * 存储数据
   */
  async setItem(key: string, value: any, options?: {
    dataType?: string;
    expiry?: number;
    encrypt?: boolean;
  }): Promise<void> {
    const dataType = options?.dataType || 'default';
    const storageType = this.getStorageType(dataType);
    const expiry = options?.expiry;

    const data: StorageData = {
      value,
      timestamp: Date.now(),
      expiry: expiry ? Date.now() + expiry : undefined
    };

    try {
      switch (storageType) {
        case StorageType.INDEXED_DB:
          // IndexedDB 通过 dataStore 处理
          const { dataStore } = await import('./indexedDBAdapter');
          await dataStore.saveSetting(key, value);
          break;

        case StorageType.LOCAL_STORAGE:
          localStorage.setItem(key, JSON.stringify(data));
          break;

        case StorageType.SESSION_STORAGE:
          sessionStorage.setItem(key, JSON.stringify(data));
          break;

        case StorageType.COOKIES:
          this.setCookie(key, JSON.stringify(data), expiry);
          break;
      }
    } catch (error) {
      console.error(`Failed to store data with key ${key}:`, error);
      throw error;
    }
  }

  /**
   * 获取数据
   */
  async getItem<T>(key: string, options?: {
    dataType?: string;
    defaultValue?: T;
  }): Promise<T | null> {
    const dataType = options?.dataType || 'default';
    const storageType = this.getStorageType(dataType);
    const defaultValue = options?.defaultValue || null;

    try {
      let rawData: string | null = null;

      switch (storageType) {
        case StorageType.INDEXED_DB:
          // IndexedDB 通过 dataStore 处理
          const { dataStore } = await import('./indexedDBAdapter');
          const value = await dataStore.getSetting(key);
          return value !== null ? value : defaultValue;

        case StorageType.LOCAL_STORAGE:
          rawData = localStorage.getItem(key);
          break;

        case StorageType.SESSION_STORAGE:
          rawData = sessionStorage.getItem(key);
          break;

        case StorageType.COOKIES:
          rawData = this.getCookie(key);
          break;
      }

      if (!rawData) {
        return defaultValue;
      }

      const data: StorageData = JSON.parse(rawData);

      // 检查是否过期
      if (data.expiry && Date.now() > data.expiry) {
        await this.removeItem(key, { dataType });
        return defaultValue;
      }

      return data.value;
    } catch (error) {
      console.error(`Failed to get data with key ${key}:`, error);
      return defaultValue;
    }
  }

  /**
   * 删除数据
   */
  async removeItem(key: string, options?: {
    dataType?: string;
  }): Promise<void> {
    const dataType = options?.dataType || 'default';
    const storageType = this.getStorageType(dataType);

    try {
      switch (storageType) {
        case StorageType.INDEXED_DB:
          const { dataStore } = await import('./indexedDBAdapter');
          await dataStore.deleteSetting(key);
          break;

        case StorageType.LOCAL_STORAGE:
          localStorage.removeItem(key);
          break;

        case StorageType.SESSION_STORAGE:
          sessionStorage.removeItem(key);
          break;

        case StorageType.COOKIES:
          this.deleteCookie(key);
          break;
      }
    } catch (error) {
      console.error(`Failed to remove data with key ${key}:`, error);
      throw error;
    }
  }

  /**
   * 清理过期数据
   */
  async cleanupExpiredData(): Promise<void> {
    // 清理 localStorage 过期数据
    this.cleanupStorage(localStorage);
    
    // 清理 sessionStorage 过期数据
    this.cleanupStorage(sessionStorage);
    
    // cookies 会自动过期，无需手动清理
  }

  /**
   * 清理指定存储的过期数据
   */
  private cleanupStorage(storage: Storage): void {
    const keysToRemove: string[] = [];

    for (let i = 0; i < storage.length; i++) {
      const key = storage.key(i);
      if (!key) continue;

      try {
        const rawData = storage.getItem(key);
        if (!rawData) continue;

        const data: StorageData = JSON.parse(rawData);
        if (data.expiry && Date.now() > data.expiry) {
          keysToRemove.push(key);
        }
      } catch (error) {
        // 如果解析失败，可能是旧格式数据，保留不删除
        console.warn(`Failed to parse storage data for key ${key}:`, error);
      }
    }

    keysToRemove.forEach(key => storage.removeItem(key));
  }

  /**
   * Cookie 操作方法
   */
  private setCookie(name: string, value: string, expiry?: number): void {
    let cookieString = `${name}=${encodeURIComponent(value)}; path=/`;
    
    if (expiry) {
      const expiryDate = new Date(Date.now() + expiry);
      cookieString += `; expires=${expiryDate.toUTCString()}`;
    }
    
    // 安全设置
    cookieString += '; SameSite=Strict';
    
    document.cookie = cookieString;
  }

  private getCookie(name: string): string | null {
    const nameEQ = name + "=";
    const ca = document.cookie.split(';');
    
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) === ' ') c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) === 0) {
        return decodeURIComponent(c.substring(nameEQ.length, c.length));
      }
    }
    return null;
  }

  private deleteCookie(name: string): void {
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
  }
}

// 导出单例实例
export const storageManager = StorageManager.getInstance();

// 便捷方法
export const storage = {
  // 主题设置
  setTheme: (theme: string) => storageManager.setItem('theme', theme, { dataType: 'theme' }),
  getTheme: () => storageManager.getItem<string>('theme', { dataType: 'theme', defaultValue: 'system' }),
  
  // 用户偏好
  setUserPreference: (key: string, value: any) => 
    storageManager.setItem(`pref_${key}`, value, { dataType: 'userPreferences' }),
  getUserPreference: <T>(key: string, defaultValue?: T) => 
    storageManager.getItem<T>(`pref_${key}`, { dataType: 'userPreferences', defaultValue }),
  
  // 会话数据
  setSessionData: (key: string, value: any) => 
    storageManager.setItem(key, value, { dataType: 'currentSession' }),
  getSessionData: <T>(key: string, defaultValue?: T) => 
    storageManager.getItem<T>(key, { dataType: 'currentSession', defaultValue }),
  
  // 认证令牌
  setAuthToken: (token: string, expiry?: number) => 
    storageManager.setItem('authToken', token, { dataType: 'authToken', expiry }),
  getAuthToken: () => 
    storageManager.getItem<string>('authToken', { dataType: 'authToken' }),
  removeAuthToken: () => 
    storageManager.removeItem('authToken', { dataType: 'authToken' }),
};
