// IndexedDB 数据库管理工具

export interface DBConfig {
  name: string;
  version: number;
  stores: {
    name: string;
    keyPath: string;
    indexes?: { name: string; keyPath: string; unique?: boolean }[];
  }[];
}

export class IndexedDBManager {
  private db: IDBDatabase | null = null;
  private config: DBConfig;

  constructor(config: DBConfig) {
    this.config = config;
  }

  // 初始化数据库
  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.config.name, this.config.version);

      request.onerror = () => {
        reject(new Error('Failed to open database'));
      };

      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        const transaction = (event.target as IDBOpenDBRequest).transaction!;

        // 创建或更新对象存储
        this.config.stores.forEach(storeConfig => {
          let store: IDBObjectStore;

          if (!db.objectStoreNames.contains(storeConfig.name)) {
            // 创建新的对象存储
            store = db.createObjectStore(storeConfig.name, {
              keyPath: storeConfig.keyPath
            });
          } else {
            // 获取现有的对象存储
            store = transaction.objectStore(storeConfig.name);
          }

          // 创建或更新索引
          if (storeConfig.indexes) {
            storeConfig.indexes.forEach(index => {
              // 检查索引是否已存在
              if (!store.indexNames.contains(index.name)) {
                try {
                  store.createIndex(index.name, index.keyPath, {
                    unique: index.unique || false
                  });
                } catch (error) {
                  console.warn(`Failed to create index ${index.name}:`, error);
                }
              }
            });
          }
        });
      };
    });
  }

  // 添加数据
  async add<T>(storeName: string, data: T): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.add(data);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error('Failed to add data'));
    });
  }

  // 更新数据
  async put<T>(storeName: string, data: T): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.put(data);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error('Failed to update data'));
    });
  }

  // 获取单个数据
  async get<T>(storeName: string, key: string): Promise<T | null> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.get(key);

      request.onsuccess = () => {
        resolve(request.result || null);
      };
      request.onerror = () => reject(new Error('Failed to get data'));
    });
  }

  // 获取所有数据
  async getAll<T>(storeName: string): Promise<T[]> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.getAll();

      request.onsuccess = () => {
        resolve(request.result || []);
      };
      request.onerror = () => reject(new Error('Failed to get all data'));
    });
  }

  // 删除数据
  async delete(storeName: string, key: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.delete(key);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error('Failed to delete data'));
    });
  }

  // 清空存储
  async clear(storeName: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.clear();

      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error('Failed to clear store'));
    });
  }

  // 通过索引查询
  async getByIndex<T>(storeName: string, indexName: string, value: any): Promise<T[]> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const index = store.index(indexName);
      const request = index.getAll(value);

      request.onsuccess = () => {
        resolve(request.result || []);
      };
      request.onerror = () => reject(new Error('Failed to get data by index'));
    });
  }

  // 计数
  async count(storeName: string): Promise<number> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.count();

      request.onsuccess = () => {
        resolve(request.result);
      };
      request.onerror = () => reject(new Error('Failed to count data'));
    });
  }

  // 关闭数据库
  close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}

// 数据库配置
export const DB_CONFIG: DBConfig = {
  name: 'ReceiptAppDB',
  version: 3, // 增加版本号以触发数据库升级
  stores: [
    {
      name: 'orders',
      keyPath: 'id',
      indexes: [
        { name: 'by_date', keyPath: 'createdAt' },
        { name: 'by_customer', keyPath: 'customer.name' },
        { name: 'by_order_number', keyPath: 'orderInfo.orderNumber', unique: true },
        { name: 'by_user', keyPath: 'userId' }
      ]
    },
    {
      name: 'products',
      keyPath: 'id',
      indexes: [
        { name: 'by_category', keyPath: 'category' },
        { name: 'by_name', keyPath: 'name' },
        { name: 'by_user', keyPath: 'userId' }
      ]
    },
    {
      name: 'business',
      keyPath: 'id',
      indexes: [
        { name: 'by_user', keyPath: 'userId' }
      ]
    },
    {
      name: 'users',
      keyPath: 'id',
      indexes: [
        { name: 'by_email', keyPath: 'email', unique: true }
      ]
    }
    // 移除 settings 表，设置信息现在存储在 localStorage 中
  ]
};

// 创建全局数据库实例
export const dbManager = new IndexedDBManager(DB_CONFIG);
