<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>离线模式 - 订单收据工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 20px;
        }

        .container {
            max-width: 400px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
        }

        h1 {
            font-size: 24px;
            margin-bottom: 16px;
            font-weight: 600;
        }

        p {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 24px;
            opacity: 0.9;
        }

        .features {
            text-align: left;
            margin: 24px 0;
        }

        .features h3 {
            font-size: 18px;
            margin-bottom: 12px;
            text-align: center;
        }

        .features ul {
            list-style: none;
            padding: 0;
        }

        .features li {
            padding: 8px 0;
            font-size: 14px;
            opacity: 0.9;
        }

        .features li:before {
            content: "✓";
            color: #4ade80;
            font-weight: bold;
            margin-right: 8px;
        }

        .retry-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .retry-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .status {
            margin-top: 20px;
            font-size: 14px;
            opacity: 0.8;
        }

        .online {
            color: #4ade80;
        }

        .offline {
            color: #f87171;
        }

        @media (max-width: 480px) {
            .container {
                padding: 30px 20px;
            }
            
            h1 {
                font-size: 20px;
            }
            
            p {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">📱</div>
        <h1>离线模式</h1>
        <p>您当前处于离线状态，但仍可以使用应用的基本功能。</p>
        
        <div class="features">
            <h3>离线可用功能</h3>
            <ul>
                <li>查看已缓存的订单</li>
                <li>创建新订单（将在联网后同步）</li>
                <li>使用商品模板</li>
                <li>生成收据预览</li>
            </ul>
        </div>

        <button class="retry-btn" onclick="checkConnection()">
            重新连接
        </button>

        <div class="status">
            网络状态: <span id="status" class="offline">离线</span>
        </div>
    </div>

    <script>
        function updateStatus() {
            const statusEl = document.getElementById('status');
            if (navigator.onLine) {
                statusEl.textContent = '在线';
                statusEl.className = 'online';
            } else {
                statusEl.textContent = '离线';
                statusEl.className = 'offline';
            }
        }

        function checkConnection() {
            if (navigator.onLine) {
                // 尝试重新加载主页
                window.location.href = '/';
            } else {
                alert('仍处于离线状态，请检查网络连接');
            }
        }

        // 监听网络状态变化
        window.addEventListener('online', () => {
            updateStatus();
            setTimeout(() => {
                window.location.href = '/';
            }, 1000);
        });

        window.addEventListener('offline', updateStatus);

        // 初始化状态
        updateStatus();

        // 定期检查网络状态
        setInterval(updateStatus, 5000);
    </script>
</body>
</html>
