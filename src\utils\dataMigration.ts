import { dbManager } from './indexedDB';
import { Order } from '@/types/order';
import { ProductTemplate } from '@/types/product';
import { BusinessInfo } from '@/types/business';
import { User } from '@/types/auth';

// 数据迁移管理器
export class DataMigrationManager {
  private static instance: DataMigrationManager;
  private migrationCompleted = false;

  static getInstance(): DataMigrationManager {
    if (!DataMigrationManager.instance) {
      DataMigrationManager.instance = new DataMigrationManager();
    }
    return DataMigrationManager.instance;
  }

  // 检查是否需要迁移
  private async needsMigration(): Promise<boolean> {
    // 检查是否已经完成过迁移
    const migrationCompleted = await this.checkMigrationStatus();
    if (migrationCompleted) {
      return false;
    }

    // 由于项目尚未发布，用户使用的数据存储方式均为IndexedDB
    // 无需从localStorage进行数据迁移
    // 只检查是否有遗留的localStorage数据需要清理
    const hasLegacyData = localStorage.getItem('order-store') ||
                         localStorage.getItem('product-store') ||
                         localStorage.getItem('business-store') ||
                         localStorage.getItem('auth-store') ||
                         localStorage.getItem('theme-store');

    return !!hasLegacyData;
  }

  // 执行数据迁移
  async migrate(): Promise<void> {
    if (this.migrationCompleted || !(await this.needsMigration())) {
      return;
    }

    console.log('开始清理遗留数据...');

    try {
      // 初始化数据库（但不依赖settings表）
      await dbManager.init();

      // 由于项目尚未发布，主要任务是清理可能存在的localStorage遗留数据
      // 如果确实需要迁移数据，可以取消注释以下代码

      // 检查并迁移可能存在的数据
      if (localStorage.getItem('order-store')) {
        await this.migrateOrders();
      }

      if (localStorage.getItem('product-store')) {
        await this.migrateProducts();
      }

      if (localStorage.getItem('business-store')) {
        await this.migrateBusiness();
      }

      if (localStorage.getItem('auth-store')) {
        await this.migrateAuth();
      }

      // 主题设置迁移到新的存储管理器
      if (localStorage.getItem('theme-store')) {
        await this.migrateThemeSettings();
      }

      // 清理localStorage数据
      await this.cleanupLocalStorage();

      // 标记迁移完成（使用新的存储管理器）
      await this.markMigrationCompleted();

      console.log('数据清理完成');
      this.migrationCompleted = true;

    } catch (error) {
      console.error('数据迁移失败:', error);
      // 即使迁移失败，也标记为完成，避免重复尝试
      try {
        await this.markMigrationCompleted();
      } catch (markError) {
        console.error('标记迁移完成失败:', markError);
      }
    }
  }

  // 迁移订单数据
  private async migrateOrders(): Promise<void> {
    const orderStoreData = localStorage.getItem('order-store');
    if (!orderStoreData) return;

    try {
      const parsed = JSON.parse(orderStoreData);
      const orders: Order[] = parsed.state?.orders || [];

      for (const order of orders) {
        await dbManager.put('orders', order);
      }

      console.log(`迁移了 ${orders.length} 个订单`);
    } catch (error) {
      console.error('迁移订单数据失败:', error);
    }
  }

  // 迁移商品数据
  private async migrateProducts(): Promise<void> {
    const productStoreData = localStorage.getItem('product-store');
    if (!productStoreData) return;

    try {
      const parsed = JSON.parse(productStoreData);
      const products: ProductTemplate[] = parsed.state?.templates || [];

      for (const product of products) {
        await dbManager.put('products', product);
      }

      console.log(`迁移了 ${products.length} 个商品模板`);
    } catch (error) {
      console.error('迁移商品数据失败:', error);
    }
  }

  // 迁移商业信息
  private async migrateBusiness(): Promise<void> {
    const businessStoreData = localStorage.getItem('business-store');
    if (!businessStoreData) return;

    try {
      const parsed = JSON.parse(businessStoreData);
      const businessInfo: BusinessInfo = parsed.state?.businessInfo;

      if (businessInfo) {
        await dbManager.put('business', businessInfo);
        console.log('迁移了商业信息');
      }
    } catch (error) {
      console.error('迁移商业信息失败:', error);
    }
  }

  // 迁移用户数据
  private async migrateAuth(): Promise<void> {
    const authStoreData = localStorage.getItem('auth-store');
    if (!authStoreData) return;

    try {
      const parsed = JSON.parse(authStoreData);
      const user: User = parsed.state?.user;

      if (user) {
        await dbManager.put('users', user);
        console.log('迁移了用户数据');
      }
    } catch (error) {
      console.error('迁移用户数据失败:', error);
    }
  }

  // 迁移主题设置到新的存储管理器
  private async migrateThemeSettings(): Promise<void> {
    const themeStoreData = localStorage.getItem('theme-store');
    if (themeStoreData) {
      try {
        const parsed = JSON.parse(themeStoreData);
        const theme = parsed.state?.theme || 'system';

        // 使用新的存储管理器保存主题设置
        const { storage } = await import('./storageManager');
        await storage.setTheme(theme);

        console.log(`迁移了主题设置: ${theme}`);
      } catch (error) {
        console.error('迁移主题设置失败:', error);
      }
    }
  }



  // 标记迁移完成
  private async markMigrationCompleted(): Promise<void> {
    try {
      // 使用新的存储管理器保存迁移状态
      const { storageManager } = await import('./storageManager');
      await storageManager.setItem('migration_completed', true, {
        dataType: 'userPreferences'
      });
    } catch (error) {
      console.error('标记迁移完成失败:', error);
    }
  }

  // 清理 localStorage 数据（自动执行）
  async cleanupLocalStorage(): Promise<void> {
    try {
      // 自动清理所有相关的localStorage数据
      const keysToRemove = [
        'order-store',
        'product-store',
        'business-store',
        'auth-store',
        'theme-store',
        'app_users',
        'app_tokens'
      ];

      keysToRemove.forEach(key => {
        if (localStorage.getItem(key)) {
          localStorage.removeItem(key);
          console.log(`已清理 localStorage 键: ${key}`);
        }
      });

      console.log('localStorage 数据清理完成');
    } catch (error) {
      console.error('清理 localStorage 数据失败:', error);
    }
  }

  // 检查迁移状态
  async checkMigrationStatus(): Promise<boolean> {
    try {
      // 使用新的存储管理器检查迁移状态
      const { storageManager } = await import('./storageManager');
      const migrationCompleted = await storageManager.getItem<boolean>('migration_completed', {
        dataType: 'userPreferences',
        defaultValue: false
      });

      // 如果没有遗留数据，直接标记为已完成
      if (!migrationCompleted) {
        const hasLegacyData = localStorage.getItem('order-store') ||
                             localStorage.getItem('product-store') ||
                             localStorage.getItem('business-store') ||
                             localStorage.getItem('auth-store') ||
                             localStorage.getItem('theme-store');

        if (!hasLegacyData) {
          // 没有遗留数据，直接标记为已完成
          await storageManager.setItem('migration_completed', true, {
            dataType: 'userPreferences'
          });
          return true;
        }
      }

      return !!migrationCompleted;
    } catch (error) {
      console.error('检查迁移状态失败:', error);
      // 出错时假设已完成，避免阻塞应用启动
      return true;
    }
  }
}

// 导出单例实例
export const dataMigration = DataMigrationManager.getInstance();
